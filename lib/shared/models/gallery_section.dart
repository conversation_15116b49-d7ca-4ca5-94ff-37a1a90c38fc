import 'package:storetrack_app/shared/models/gallery_photo.dart';

class GallerySection {
  List<GalleryPhoto> items;
  String? photoTag;
  int numberOfPhotos;
  int photoCompression;
  String? photoTagID;
  bool isMandatory;
  bool liveImagesOnly;
  String? measurementPhotoTypeID;
  String? combineTypeID;

  GallerySection({
    this.items = const [],
    this.photoTag,
    this.numberOfPhotos = 0,
    this.photoCompression = 0,
    this.photoTagID,
    this.isMandatory = false,
    this.liveImagesOnly = false,
    this.measurementPhotoTypeID,
    this.combineTypeID,
  });

  /// All required photos are uploaded
  bool get isFinished {
    if (!isMandatory) return true;

    // TODO: Replace this with actual business logic from commented iOS/Android logic
    return false;
  }

  /// Considered finished if it has any photo or some condition
  bool get isConsideredFinished {
    // Placeholder logic. Replace with actual as needed
    return true;
  }

  bool get hasPhotoUnableToLoad {
    return items.any((item) => item.isCannotUploadMandatory);
  }

  @override
  String toString() {
    final buffer = StringBuffer()
      ..writeln("Gallery section size: ${items.length}")
      ..writeln("Gallery photos---------");

    for (final photo in items) {
      buffer.writeln(photo.toString());
    }

    buffer.writeln("---------------");
    return buffer.toString();
  }

  factory GallerySection.fromJson(Map<String, dynamic> json) {
    return GallerySection(
      items: (json['items'] as List<dynamic>?)
              ?.map((item) => GalleryPhoto.fromJson(item))
              .toList() ??
          [],
      photoTag: json['photoTag'],
      numberOfPhotos: json['numberOfPhotos'] ?? 0,
      photoCompression: json['photoCompression'] ?? 0,
      photoTagID: json['photoTagID'],
      isMandatory: json['isMandatory'] ?? false,
      liveImagesOnly: json['liveImagesOnly'] ?? false,
      measurementPhotoTypeID: json['measurementPhotoTypeID'],
      combineTypeID: json['combineTypeID'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'items': items.map((e) => e.toJson()).toList(),
      'photoTag': photoTag,
      'numberOfPhotos': numberOfPhotos,
      'photoCompression': photoCompression,
      'photoTagID': photoTagID,
      'isMandatory': isMandatory,
      'liveImagesOnly': liveImagesOnly,
      'measurementPhotoTypeID': measurementPhotoTypeID,
      'combineTypeID': combineTypeID,
    };
  }
}
