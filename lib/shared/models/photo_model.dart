class PhotoModel {
  static const String photoIDKey = "photoID";
  static const String photoURLKey = "photoURL";
  static const String photoLocalPathKey = "photoLocalPath";
  static const String photoIsDeletedKey = "photoIsDeleted";

  String? photoID;
  String? formID;
  String? questionID;
  String? measurementID;

  String? photoCaption;
  String? photoURL;
  String? photoLocalPath;
  bool isCannotUploadMandatory = false;
  DateTime? photoLastModifiedDate;

  bool isPhotoCaptionUpdated = false;
  bool isPhotoItemUpdated = false;
  bool photoIsDeleted = false;

  String? questionPartID;
  String? questionMultiPartID;
  String? measurementPhotoTypeID;
  String? combineTypeID;
  String? photoDate;

  PhotoModel();

  bool get isDummyPhoto => photoCaption == "DummyImageAddPhoto"; // Replace with localized string if needed

  String? get effectiveQuestionMultiPartID => questionMultiPartID?.isNotEmpty == true ? questionMultiPartID : questionPartID;

  factory PhotoModel.fromJson(Map<String, dynamic> json) {
    return PhotoModel()
      ..photoID = json['photoID']
      ..formID = json['formID']
      ..questionID = json['questionID']
      ..measurementID = json['measurementID']
      ..photoCaption = json['photoCaption']
      ..photoURL = json['photoURL']
      ..photoLocalPath = json['photoLocalPath']
      ..isCannotUploadMandatory = json['isCannotUploadMandatory'] ?? false
      ..photoLastModifiedDate = json['photoLastModifiedDate'] != null
          ? DateTime.parse(json['photoLastModifiedDate'])
          : null
      ..isPhotoCaptionUpdated = json['isPhotoCaptionUpdated'] ?? false
      ..isPhotoItemUpdated = json['isPhotoItemUpdated'] ?? false
      ..photoIsDeleted = json['photoIsDeleted'] ?? false
      ..questionPartID = json['questionPartID']
      ..questionMultiPartID = json['questionMultiPartID']
      ..measurementPhotoTypeID = json['measurementPhotoTypeID']
      ..combineTypeID = json['combineTypeID']
      ..photoDate = json['photoDate'];
  }

  Map<String, dynamic> toJson() {
    return {
      'photoID': photoID,
      'formID': formID,
      'questionID': questionID,
      'measurementID': measurementID,
      'photoCaption': photoCaption,
      'photoURL': photoURL,
      'photoLocalPath': photoLocalPath,
      'isCannotUploadMandatory': isCannotUploadMandatory,
      'photoLastModifiedDate': photoLastModifiedDate?.toIso8601String(),
      'isPhotoCaptionUpdated': isPhotoCaptionUpdated,
      'isPhotoItemUpdated': isPhotoItemUpdated,
      'photoIsDeleted': photoIsDeleted,
      'questionPartID': questionPartID,
      'questionMultiPartID': questionMultiPartID,
      'measurementPhotoTypeID': measurementPhotoTypeID,
      'combineTypeID': combineTypeID,
      'photoDate': photoDate,
    };
  }
}
