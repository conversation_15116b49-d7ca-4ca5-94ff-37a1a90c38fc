import 'package:storetrack_app/shared/models/photo_model.dart';

class PhotoFolderModel {
  static const String photoFolderIDKey = "photoFolderID";

  String photoFolderID;
  String photoFolderName;
  int photoFolderMaxAmount;
  bool isPhotoFolderMandatory;
  DateTime photoFolderLastModifiedDate;
  bool imageRec;
  List<PhotoModel> photoFolderContents;

  PhotoFolderModel({
    required this.photoFolderID,
    required this.photoFolderName,
    required this.photoFolderMaxAmount,
    required this.isPhotoFolderMandatory,
    required this.photoFolderLastModifiedDate,
    required this.imageRec,
    required this.photoFolderContents,
  });

  factory PhotoFolderModel.fromJson(Map<String, dynamic> json) {
    return PhotoFolderModel(
      photoFolderID: json['photoFolderID'],
      photoFolderName: json['photoFolderName'],
      photoFolderMaxAmount: json['photoFolderMaxAmount'],
      isPhotoFolderMandatory: json['isPhotoFolderMandatory'],
      photoFolderLastModifiedDate: DateTime.parse(json['photoFolderLastModifiedDate']),
      imageRec: json['imageRec'] ?? false,
      photoFolderContents: (json['photoFolderContents'] as List<dynamic>?)
              ?.map((e) => PhotoModel.fromJson(e))
              .toList() ??
          [],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'photoFolderID': photoFolderID,
      'photoFolderName': photoFolderName,
      'photoFolderMaxAmount': photoFolderMaxAmount,
      'isPhotoFolderMandatory': isPhotoFolderMandatory,
      'photoFolderLastModifiedDate': photoFolderLastModifiedDate.toIso8601String(),
      'imageRec': imageRec,
      'photoFolderContents': photoFolderContents.map((e) => e.toJson()).toList(),
    };
  }
}
