class GalleryPhoto {
  String? photoID;
  String? formID;
  String? questionID;
  String? measurementID;

  String? photoCaption;
  String? photoURL;
  String? photoLocalPath;

  bool isCannotUploadMandatory;
  bool isLiveImagesOnly;
  bool isSynced;
  bool isAddImageButton;
  bool isPhotoTagMandatory;
  bool imageRec;

  String? questionPartID;
  String? measurementPhotoTypeID;
  String? combineTypeID;
  String? questionMultiPartID;

  String? photoTagID;
  String? taskID;
  String? photoTag;

  int photoCompression;

  GalleryPhoto({
    this.photoID,
    this.formID,
    this.questionID,
    this.measurementID,
    this.photoCaption,
    this.photoURL,
    this.photoLocalPath,
    this.isCannotUploadMandatory = false,
    this.isLiveImagesOnly = false,
    this.isSynced = false,
    this.isAddImageButton = false,
    this.isPhotoTagMandatory = false,
    this.imageRec = false,
    this.questionPartID,
    this.measurementPhotoTypeID,
    this.combineTypeID,
    this.questionMultiPartID,
    this.photoTagID,
    this.taskID,
    this.photoTag,
    this.photoCompression = 0,
  });

  @override
  String toString() {
    return '{photoID: $photoID, photoCaption: $photoCaption, photoURL: $photoURL, photoLocalPath: $photoLocalPath}';
  }

  factory GalleryPhoto.fromJson(Map<String, dynamic> json) {
    return GalleryPhoto(
      photoID: json['photoID'],
      formID: json['formID'],
      questionID: json['questionID'],
      measurementID: json['measurementID'],
      photoCaption: json['photoCaption'],
      photoURL: json['photoURL'],
      photoLocalPath: json['photoLocalPath'],
      isCannotUploadMandatory: json['isCannotUploadMandatory'] ?? false,
      isLiveImagesOnly: json['isLiveImagesOnly'] ?? false,
      isSynced: json['isSynced'] ?? false,
      isAddImageButton: json['isAddImageButton'] ?? false,
      isPhotoTagMandatory: json['isPhotoTagMandatory'] ?? false,
      imageRec: json['imageRec'] ?? false,
      questionPartID: json['questionPartID'],
      measurementPhotoTypeID: json['measurementPhotoTypeID'],
      combineTypeID: json['combineTypeID'],
      questionMultiPartID: json['questionMultiPartID'],
      photoTagID: json['photoTagID'],
      taskID: json['taskID'],
      photoTag: json['photoTag'],
      photoCompression: json['photoCompression'] ?? 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'photoID': photoID,
      'formID': formID,
      'questionID': questionID,
      'measurementID': measurementID,
      'photoCaption': photoCaption,
      'photoURL': photoURL,
      'photoLocalPath': photoLocalPath,
      'isCannotUploadMandatory': isCannotUploadMandatory,
      'isLiveImagesOnly': isLiveImagesOnly,
      'isSynced': isSynced,
      'isAddImageButton': isAddImageButton,
      'isPhotoTagMandatory': isPhotoTagMandatory,
      'imageRec': imageRec,
      'questionPartID': questionPartID,
      'measurementPhotoTypeID': measurementPhotoTypeID,
      'combineTypeID': combineTypeID,
      'questionMultiPartID': questionMultiPartID,
      'photoTagID': photoTagID,
      'taskID': taskID,
      'photoTag': photoTag,
      'photoCompression': photoCompression,
    };
  }
}
