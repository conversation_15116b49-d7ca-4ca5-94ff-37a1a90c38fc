import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:storetrack_app/config/themes/app_colors.dart';
import 'package:storetrack_app/core/extensions/theme_extensions.dart';
import 'package:storetrack_app/features/home/<USER>/entities/tasks_response_entity.dart';
import 'package:storetrack_app/features/home/<USER>/widgets/photo_widget.dart'
    show PhotoUploadWidget;

class CheckboxWidget extends StatelessWidget {
  final Measurement measurement;
  final bool value;
  final Function(bool) onChanged;
  final bool showCameraIcon;
  final bool isCameraMandatory;
  final VoidCallback? onCameraTap;
  final bool isRequired;

  const CheckboxWidget({
    super.key,
    required this.measurement,
    required this.value,
    required this.onChanged,
    this.showCameraIcon = false,
    this.isCameraMandatory = false,
    this.onCameraTap,
    this.isRequired = false,
  });

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;

    return Container(
      width: double.infinity,
      margin: const EdgeInsets.symmetric(vertical: 4.0),
      padding: const EdgeInsets.all(16.0),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: AppColors.black10,
            blurRadius: 4,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Title with required indicator
          Row(
            children: [
              Expanded(
                child: Text(
                  measurement.measurementDescription ?? 'Checkbox',
                  style: textTheme.montserratTitleExtraSmall,
                ),
              ),
              if (isRequired)
                Container(
                  width: 16,
                  height: 16,
                  decoration: const BoxDecoration(
                    color: Colors.red,
                    shape: BoxShape.circle,
                  ),
                  child: const Icon(
                    Icons.priority_high,
                    color: Colors.white,
                    size: 10,
                  ),
                ),
            ],
          ),
          const Gap(16),
          GestureDetector(
            onTap: () => onChanged(!value),
            child: Row(
              children: [
                Container(
                  width: 20,
                  height: 20,
                  decoration: BoxDecoration(
                    color: value ? AppColors.primaryBlue : Colors.white,
                    borderRadius: BorderRadius.circular(2),
                    border: Border.all(
                      color:
                          value ? AppColors.primaryBlue : AppColors.blackTint2,
                      width: 2,
                    ),
                  ),
                  child: value
                      ? const Icon(
                          Icons.check,
                          size: 14,
                          color: Colors.white,
                        )
                      : null,
                ),
                const Gap(12),
                Expanded(
                  child: Text(
                    measurement.measurementDescription ?? 'Check this option',
                    style: textTheme.bodyMedium?.copyWith(
                      color: AppColors.black,
                      fontSize: 15,
                    ),
                  ),
                ),
              ],
            ),
          ),
          // Camera section
          if (showCameraIcon) ...[
            const Gap(16),
            PhotoUploadWidget(
              onCameraPressed: () {
                // Handle camera button press
                print('Camera button pressed1');
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('Camera button pressed'),
                    duration: Duration(seconds: 1),
                  ),
                );
              },
            ),
            // Row(
            //   children: [
            //     // HI-RES Button
            //     Container(
            //       padding: const EdgeInsets.symmetric(
            //         horizontal: 12.0,
            //         vertical: 6.0,
            //       ),
            //       decoration: BoxDecoration(
            //         color: AppColors.lightGrey2,
            //         borderRadius: BorderRadius.circular(6),
            //         border: Border.all(
            //           color: AppColors.blackTint2,
            //           width: 1,
            //         ),
            //       ),
            //       child: Text(
            //         'HI-RES',
            //         style: textTheme.bodySmall?.copyWith(
            //           color: AppColors.blackTint1,
            //           fontSize: 11,
            //           fontWeight: FontWeight.w600,
            //         ),
            //       ),
            //     ),
            //     const Gap(12),
            //     // Camera Icon
            //     GestureDetector(
            //       onTap: onCameraTap,
            //       child: Container(
            //         width: 40,
            //         height: 40,
            //         decoration: BoxDecoration(
            //           color: isCameraMandatory ? Colors.amber : Colors.green,
            //           borderRadius: BorderRadius.circular(8),
            //         ),
            //         child: const Icon(
            //           Icons.camera_alt,
            //           color: Colors.white,
            //           size: 20,
            //         ),
            //       ),
            //     ),
            //   ],
            // ),
          ],
          if (measurement.required == true && !value)
            Padding(
              padding: const EdgeInsets.only(top: 12.0),
              child: Text(
                'This field is required',
                style: textTheme.bodySmall?.copyWith(
                  color: Colors.red,
                  fontSize: 12,
                ),
              ),
            ),
        ],
      ),
    );
  }
}
